package com.kingbase8;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;

public class TestResultSet {

    protected String Driver = "com.kingbase8.Driver";
    protected static String Url = "*************************************";
    protected Connection connection = null;
    protected Statement statement = null;
    protected void Driver() throws ClassNotFoundException
    {
        Class.forName(Driver);
    }
    protected void connection(String url) throws SQLException,
            InterruptedException
    {
        connection = DriverManager.getConnection(url, "system", "manager");
        if(connection != null)
        {
            System.out.println("connection sucessful!");
        }
        else
        {
            System.out.println("connection fail!");
        }
    }
    protected void statement() throws SQLException
    {
        statement = connection.createStatement();
    }
    protected void table() throws SQLException
    {
        statement.executeUpdate("create table table1(id int primary key," +
                "name char(10))");
        statement.executeUpdate("insert into table1 values (1,'KingbaseES')");
    }
    public static void main(String[] args) throws ClassNotFoundException,
            SQLException, InterruptedException
    {
        TestResultSet test = new TestResultSet();
        test.Driver();
        test.connection(Url);
        test.statement();
        test.table();
        test.resultSet();
        test.updataResultSet();
        test.close();
    }
    protected void resultSet() throws SQLException
    {
        ResultSet rs = statement.executeQuery("select * from table1");
        while(rs.next())
        {
            System.out.println(rs.getInt(1));
        }
        ResultSetMetaData rsmd = rs.getMetaData();
        System.out.println(rsmd.getColumnTypeName(1));
        rs.close();
    }
    protected void updataResultSet() throws SQLException
    {
        Statement stmt = connection.createStatement(
                ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
        ResultSet rs = stmt.executeQuery("select * from table1");
        rs.next();
        System.out.println("update before: " + rs.getInt(1));
        rs.updateInt(1, 21);
        rs.updateRow();
        System.out.println("update after: " + rs.getInt(1));
        rs.close();
        stmt.close();
    }
    protected void close() throws SQLException
    {
        statement.executeUpdate("drop table table1");
        if(statement != null)
        {
            statement.close();
        }
        if(connection != null)
        {
            connection.close();
        }
    }
}
