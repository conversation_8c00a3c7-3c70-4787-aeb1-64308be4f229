# 人大金仓存储过程调用示例

本目录包含了使用JDBC调用人大金仓数据库存储过程的完整示例，特别针对 `vxspd.px_route.p_route_json` 存储过程。

## 文件说明

### 1. TestStoredProcedure.java
基础的存储过程调用示例，包含：
- 基本的数据库连接
- CallableStatement 调用存储过程
- 匿名块调用方式
- 输入输出参数处理

### 2. AdvancedStoredProcedureExample.java
高级存储过程调用示例，包含：
- 完整的错误处理和事务管理
- 批量调用存储过程
- 结果集处理
- 性能监控
- 临时表结果存储

### 3. KingbaseStoredProcedureUtil.java
存储过程调用工具类，提供：
- 便捷的连接管理
- 封装的存储过程调用方法
- 结果对象封装
- 事务管理工具方法

## 数据库配置

```java
// 连接配置
String host = "**************";
int port = 54321;
String database = "spd";
String username = "vxspd";
String password = "vxspd";
```

## 存储过程签名

```sql
call "vxspd"."px_route".p_route_json(
    "m_json_args" varchar,              -- 输入参数：JSON字符串
    "m_json_argsdetail" type_nvarchar2, -- 输入参数：详细参数
    "m_json_retn" varchar,              -- 输出参数：JSON返回值
    "m_json_args_retn" type_nvarchar2,  -- 输出参数：参数返回值
    "m_sql_retn" varchar                -- 输出参数：SQL返回值
)
```

## 使用示例

### 基础调用
```java
TestStoredProcedure test = new TestStoredProcedure();
test.loadDriver();
test.connect();
test.callRouteJsonProcedure();
test.closeConnection();
```

### 高级调用
```java
AdvancedStoredProcedureExample example = new AdvancedStoredProcedureExample();
example.initialize();

Map<String, String> results = example.callRouteJsonWithFullHandling(
    "{\"action\":\"test\",\"data\":\"sample\"}", 
    "详细参数"
);

example.cleanup();
```

### 工具类调用
```java
KingbaseStoredProcedureUtil util = new KingbaseStoredProcedureUtil(
    "**************", 54321, "spd", "vxspd", "vxspd"
);

util.connect();
StoredProcedureResult result = util.callRouteJsonProcedure(
    "{\"action\":\"test\"}", 
    "测试参数"
);

System.out.println(result.getJsonRetn());
util.close();
```

## 参数说明

### 输入参数
- **m_json_args**: JSON格式的主要参数，通常包含业务逻辑相关的数据
- **m_json_argsdetail**: 详细参数，使用自定义类型 `vxspd.type_nvarchar2`

### 输出参数
- **m_json_retn**: 存储过程的主要返回结果，JSON格式
- **m_json_args_retn**: 参数相关的返回信息
- **m_sql_retn**: SQL执行相关的返回信息

## 注意事项

1. **自定义类型处理**: `vxspd.type_nvarchar2` 是数据库中定义的自定义类型，在JDBC中作为VARCHAR处理

2. **事务管理**: 建议使用手动事务提交模式，确保数据一致性

3. **连接池**: 生产环境建议使用连接池管理数据库连接

4. **错误处理**: 务必进行适当的异常处理和事务回滚

5. **资源释放**: 确保及时关闭Statement、ResultSet和Connection

## 编译和运行

```bash
# 编译
javac -cp "lib/kingbase8-9.0.0.jar" src/main/java/com/kingbase8/*.java

# 运行基础示例
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.TestStoredProcedure

# 运行高级示例
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.AdvancedStoredProcedureExample

# 运行工具类示例
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.KingbaseStoredProcedureUtil
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务是否启动
   - 验证IP地址、端口、数据库名
   - 确认用户名密码正确

2. **存储过程不存在**
   - 确认存储过程已在数据库中创建
   - 检查包名和存储过程名的大小写
   - 验证用户是否有执行权限

3. **参数类型错误**
   - 确认自定义类型 `vxspd.type_nvarchar2` 已定义
   - 检查参数的数据类型匹配

4. **编码问题**
   - 确保连接字符串包含正确的编码设置
   - 检查数据库和客户端的字符集配置

### 调试建议

1. 启用JDBC日志记录
2. 使用数据库客户端工具验证存储过程
3. 检查数据库日志文件
4. 使用简单的测试参数进行调试
