# 人大金仓存储过程调用示例

本目录包含了使用JDBC调用人大金仓数据库存储过程的完整示例，特别针对 `vxspd.px_route.p_route_json` 存储过程。

## 文件说明

### 1. TestStoredProcedure.java
基础的存储过程调用示例，包含：
- 基本的数据库连接
- CallableStatement 调用存储过程
- 匿名块调用方式
- 输入输出参数处理

### 2. ArrayTypeStoredProcedureExample.java ⭐ **推荐**
专门处理 type_nvarchar2 数组类型的示例，包含：
- 正确处理 VARCHAR 数组类型参数
- 多种数组参数传递方式
- 匿名块中的数组类型构造
- 不同格式的数组参数测试

### 3. KingbaseStoredProcedureUtil.java
存储过程调用工具类，提供：
- 便捷的连接管理
- 封装的存储过程调用方法（支持数组类型）
- 结果对象封装
- 事务管理工具方法

### 4. QuickConnectionTest.java
快速连接测试工具：
- 验证数据库连接
- 检查存储过程是否存在
- 验证自定义类型定义

### 5. AdvancedStoredProcedureExample.java
高级存储过程调用示例，包含：
- 完整的错误处理和事务管理
- 批量调用存储过程
- 结果集处理
- 性能监控
- 临时表结果存储

## 数据库配置

```java
// 连接配置
String host = "**************";
int port = 54321;
String database = "spd";
String username = "vxspd";
String password = "vxspd";
```

## 存储过程签名

```sql
call "vxspd"."px_route".p_route_json(
    "m_json_args" varchar,              -- 输入参数：JSON字符串
    "m_json_argsdetail" type_nvarchar2, -- 输入参数：详细参数（VARCHAR数组类型）
    "m_json_retn" varchar,              -- 输出参数：JSON返回值
    "m_json_args_retn" type_nvarchar2,  -- 输出参数：参数返回值（VARCHAR数组类型）
    "m_sql_retn" varchar                -- 输出参数：SQL返回值
)
```

## 重要：type_nvarchar2 数组类型说明

`vxspd.type_nvarchar2` 是一个**可变数组类型**，子类型是 `varchar`。在 JDBC 中处理时需要注意：

### 特点
- 这是一个自定义的 VARCHAR 数组类型
- 可以存储多个字符串值
- 在 JDBC 中通常作为字符串处理

### JDBC 处理方式
1. **推荐方式**：作为字符串传递
   ```java
   stmt.setString(2, "Array parameter as string");
   ```

2. **备选方式**：尝试使用 Array 对象
   ```java
   String[] arrayElements = {"element1", "element2", "element3"};
   Array sqlArray = connection.createArrayOf("varchar", arrayElements);
   stmt.setArray(2, sqlArray);
   ```

3. **多种格式支持**：
   - 单个字符串：`"Single value"`
   - 逗号分隔：`"value1,value2,value3"`
   - JSON格式：`"{\"array\":[\"val1\",\"val2\"]}"`

详细的数组类型处理说明请参考：`ArrayTypeHandling_README.md`

## 使用示例

### 快速连接测试（推荐先运行）
```java
// 验证数据库连接和存储过程是否存在
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.QuickConnectionTest
```

### 数组类型处理示例（推荐）
```java
ArrayTypeStoredProcedureExample example = new ArrayTypeStoredProcedureExample();
example.initialize();

// 测试不同的数组参数处理方式
example.callRouteJsonWithArrayType();
example.callRouteJsonWithArrayObject();
example.testArrayTypeHandling();

example.cleanup();
```

### 工具类调用（最简单）
```java
KingbaseStoredProcedureUtil util = new KingbaseStoredProcedureUtil(
    "**************", 54321, "spd", "vxspd", "vxspd"
);

util.connect();

// 方式1：字符串参数
StoredProcedureResult result1 = util.callRouteJsonProcedure(
    "{\"action\":\"test\"}",
    "Array parameter as string"  // type_nvarchar2 作为字符串
);

// 方式2：数组参数
String[] arrayParams = {"param1", "param2", "param3"};
StoredProcedureResult result2 = util.callRouteJsonProcedureWithArray(
    "{\"action\":\"array_test\"}",
    arrayParams  // type_nvarchar2 作为数组
);

System.out.println("Result: " + result1.getJsonRetn());
util.close();
```

### 基础调用
```java
TestStoredProcedure test = new TestStoredProcedure();
test.loadDriver();
test.connect();
test.callRouteJsonProcedure();
test.closeConnection();
```

## 参数说明

### 输入参数
- **m_json_args**: JSON格式的主要参数，通常包含业务逻辑相关的数据
- **m_json_argsdetail**: 详细参数，使用自定义类型 `vxspd.type_nvarchar2`

### 输出参数
- **m_json_retn**: 存储过程的主要返回结果，JSON格式
- **m_json_args_retn**: 参数相关的返回信息
- **m_sql_retn**: SQL执行相关的返回信息

## 注意事项

1. **自定义类型处理**: `vxspd.type_nvarchar2` 是数据库中定义的自定义类型，在JDBC中作为VARCHAR处理

2. **事务管理**: 建议使用手动事务提交模式，确保数据一致性

3. **连接池**: 生产环境建议使用连接池管理数据库连接

4. **错误处理**: 务必进行适当的异常处理和事务回滚

5. **资源释放**: 确保及时关闭Statement、ResultSet和Connection

## 编译和运行

```bash
# 编译
javac -cp "lib/kingbase8-9.0.0.jar" src/main/java/com/kingbase8/*.java

# 运行基础示例
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.TestStoredProcedure

# 运行高级示例
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.AdvancedStoredProcedureExample

# 运行工具类示例
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.KingbaseStoredProcedureUtil
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务是否启动
   - 验证IP地址、端口、数据库名
   - 确认用户名密码正确

2. **存储过程不存在**
   - 确认存储过程已在数据库中创建
   - 检查包名和存储过程名的大小写
   - 验证用户是否有执行权限

3. **参数类型错误**
   - 确认自定义类型 `vxspd.type_nvarchar2` 已定义
   - 检查参数的数据类型匹配

4. **编码问题**
   - 确保连接字符串包含正确的编码设置
   - 检查数据库和客户端的字符集配置

### 调试建议

1. 启用JDBC日志记录
2. 使用数据库客户端工具验证存储过程
3. 检查数据库日志文件
4. 使用简单的测试参数进行调试
