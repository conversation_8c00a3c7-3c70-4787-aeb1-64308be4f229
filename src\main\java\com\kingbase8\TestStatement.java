package com.kingbase8;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class TestStatement {

    protected String Driver = "com.kingbase8.Driver";
    protected static String Url = "*************************************";
    protected Connection connection = null;
    protected Statement statement = null;
    protected String create_table = "create table orders(ORDER_ID SERIAL, "
            + "ISBN int, CUSTOMERID varchar(20))";

    protected void Driver() throws ClassNotFoundException {
        Class.forName(Driver);
    }

    public static void main(String[] args) throws ClassNotFoundException,
            SQLException, InterruptedException {
        TestStatement test = new TestStatement();
        test.Driver();
        test.connection(Url);
        test.statement();
        test.table();
        test.getGeneratedKeys();
        test.close();
    }

    protected void connection(String url) throws SQLException,
            InterruptedException {
        connection = DriverManager.getConnection(url, "system", "manager");
        if (connection != null) {
            System.out.println("connection sucessful!");
        } else {
            System.out.println("connection fail!");
        }
    }

    protected void statement() throws SQLException {
        statement = connection.createStatement();
    }

    protected void table() throws SQLException {
        statement.executeUpdate(create_table);
    }

    protected void getGeneratedKeys() throws SQLException {
        int rows = statement.executeUpdate("insert into orders (ISBN," +
                        "CUSTOMERID) VALUES (195123018,'BILLING')"
                , Statement.RETURN_GENERATED_KEYS);
        System.out.println("rows:" + rows);
        ResultSet rs = null;
        rs = statement.getGeneratedKeys();
        boolean b = rs.next();
        if (b) {
            System.out.println(rs.getString(1));
        }
        rs.close();
        statement.executeUpdate("delete from orders");
        boolean result = statement.execute("insert into orders (ISBN," +
                        "CUSTOMERID) VALUES (195123018,'BILLING')"
                , Statement.RETURN_GENERATED_KEYS);
        System.out.println("result:" + result);
        rs = statement.getGeneratedKeys();
        boolean c = rs.next();
        if (c) {
            System.out.println(rs.getString(1));
        }
        rs.close();
        statement.executeUpdate("delete from orders");
        String keyColumn[] = {"order_id"};
        int row = statement.executeUpdate("insert into orders (ISBN," +
                "CUSTOMERID) VALUES (195123018,'BILLING')", keyColumn);
        System.out.println("row:" + row);
        rs = statement.getGeneratedKeys();
        boolean d = rs.next();
        if (d) {
            System.out.println(rs.getString(1));
        }
        rs.close();
        statement.executeUpdate("delete from orders");
        String keyColumn1[] = {"order_id"};
        boolean result1 = statement.execute("insert into orders (ISBN," +
                "CUSTOMERID) VALUES (195123018,'BILLING')", keyColumn1);
        System.out.println("result1:" + result1);
        rs = statement.getGeneratedKeys();
        boolean e = rs.next();
        if (e) {
            System.out.println(rs.getString(1));
        }
        rs.close();
    }

    protected void close() throws SQLException {
        statement.executeUpdate("drop table orders");
        if (statement != null) {
            statement.close();
        }
        if (connection != null) {
            connection.close();
        }
    }
}
