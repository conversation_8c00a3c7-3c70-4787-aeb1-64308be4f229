package com.kingbase8;

import java.sql.*;

/**
 * Quick connection test for Kingbase database
 * Tests basic connectivity before running stored procedure examples
 */
public class QuickConnectionTest {
    
    private static final String DRIVER = "com.kingbase8.Driver";
    private static final String URL = "*****************************************";
    private static final String USERNAME = "vxspd";
    private static final String PASSWORD = "vxspd";
    
    public static void main(String[] args) {
        System.out.println("=== Kingbase Database Connection Test ===");
        
        Connection connection = null;
        
        try {
            // Load driver
            System.out.println("1. Loading database driver...");
            Class.forName(DRIVER);
            System.out.println("   Driver loaded successfully");
            
            // Connect to database
            System.out.println("2. Connecting to database...");
            System.out.println("   URL: " + URL);
            System.out.println("   Username: " + USERNAME);
            
            connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            System.out.println("   Connection successful!");
            
            // Test basic query
            System.out.println("3. Testing basic query...");
            try (Statement stmt = connection.createStatement()) {
                ResultSet rs = stmt.executeQuery("SELECT CURRENT_TIMESTAMP, USER, DATABASE()");
                if (rs.next()) {
                    System.out.println("   Current time: " + rs.getTimestamp(1));
                    System.out.println("   Current user: " + rs.getString(2));
                    System.out.println("   Current database: " + rs.getString(3));
                }
                rs.close();
            }
            
            // Test stored procedure existence
            System.out.println("4. Checking stored procedure...");
            try (Statement stmt = connection.createStatement()) {
                // Check if the stored procedure exists
                String checkProcSql = 
                    "SELECT COUNT(*) FROM information_schema.routines " +
                    "WHERE routine_schema = 'vxspd' AND routine_name = 'p_route_json'";
                
                ResultSet rs = stmt.executeQuery(checkProcSql);
                if (rs.next()) {
                    int count = rs.getInt(1);
                    if (count > 0) {
                        System.out.println("   Stored procedure 'vxspd.px_route.p_route_json' found");
                    } else {
                        System.out.println("   Warning: Stored procedure 'vxspd.px_route.p_route_json' not found");
                        System.out.println("   Please verify the procedure exists in the database");
                    }
                }
                rs.close();
            } catch (SQLException e) {
                System.out.println("   Could not check stored procedure: " + e.getMessage());
            }
            
            // Test custom type
            System.out.println("5. Checking custom type...");
            try (Statement stmt = connection.createStatement()) {
                String checkTypeSql = 
                    "SELECT COUNT(*) FROM information_schema.user_defined_types " +
                    "WHERE user_defined_type_schema = 'vxspd' AND user_defined_type_name = 'type_nvarchar2'";
                
                ResultSet rs = stmt.executeQuery(checkTypeSql);
                if (rs.next()) {
                    int count = rs.getInt(1);
                    if (count > 0) {
                        System.out.println("   Custom type 'vxspd.type_nvarchar2' found");
                    } else {
                        System.out.println("   Warning: Custom type 'vxspd.type_nvarchar2' not found");
                        System.out.println("   The type might be defined differently or in a different schema");
                    }
                }
                rs.close();
            } catch (SQLException e) {
                System.out.println("   Could not check custom type: " + e.getMessage());
            }
            
            System.out.println("\n=== Connection test completed successfully ===");
            System.out.println("You can now run the stored procedure examples:");
            System.out.println("- ArrayTypeStoredProcedureExample");
            System.out.println("- KingbaseStoredProcedureUtil");
            System.out.println("- TestStoredProcedure");
            
        } catch (ClassNotFoundException e) {
            System.err.println("Error: Database driver not found");
            System.err.println("Make sure kingbase8-9.0.0.jar is in the classpath");
            System.err.println("Details: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("Error: Database connection failed");
            System.err.println("Error code: " + e.getErrorCode());
            System.err.println("SQL state: " + e.getSQLState());
            System.err.println("Message: " + e.getMessage());
            System.err.println("\nPossible causes:");
            System.err.println("- Database server is not running");
            System.err.println("- Incorrect host/port/database name");
            System.err.println("- Invalid username/password");
            System.err.println("- Network connectivity issues");
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Close connection
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("Connection closed");
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }
    }
}
