# 人大金仓 type_nvarchar2 数组类型处理指南

## 概述

`vxspd.type_nvarchar2` 是人大金仓数据库中定义的自定义数组类型，其子类型是 `varchar`。这意味着它是一个可变长度字符串数组类型。

## 类型定义

```sql
-- type_nvarchar2 是一个可变数组类型
-- 子类型: varchar
-- 用法: type_nvarchar2 可以存储多个 varchar 值
```

## JDBC 处理方式

### 1. 作为字符串传递（推荐）

最简单和最常用的方法是将数组参数作为字符串传递：

```java
CallableStatement stmt = connection.prepareCall("{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }");

// 输入参数
stmt.setString(1, jsonArgs);                    // varchar 类型
stmt.setString(2, "Array detail as string");   // type_nvarchar2 作为字符串

// 输出参数
stmt.registerOutParameter(3, Types.VARCHAR);   // varchar
stmt.registerOutParameter(4, Types.VARCHAR);   // type_nvarchar2 返回为字符串
stmt.registerOutParameter(5, Types.VARCHAR);   // varchar
```

### 2. 使用 Array 对象（备选方案）

尝试创建 SQL Array 对象：

```java
try {
    String[] arrayElements = {"element1", "element2", "element3"};
    Array sqlArray = connection.createArrayOf("varchar", arrayElements);
    stmt.setArray(2, sqlArray);
} catch (SQLException e) {
    // 如果 Array 创建失败，回退到字符串方式
    stmt.setString(2, String.join(",", arrayElements));
}
```

### 3. 不同的字符串格式

`type_nvarchar2` 参数可以接受多种字符串格式：

```java
// 单个字符串值
stmt.setString(2, "Single string value");

// 逗号分隔的值
stmt.setString(2, "value1,value2,value3");

// JSON 数组格式
stmt.setString(2, "{\"array\":[\"val1\",\"val2\",\"val3\"]}");

// 包含特殊字符的值
stmt.setString(2, "Value with special chars: !@#$%^&*()");
```

## 匿名块中的使用

在匿名块中，需要使用类型构造函数：

```sql
DECLARE
  m_json_args varchar := ?;
  m_json_argsdetail "vxspd"."type_nvarchar2" := "vxspd"."type_nvarchar2"(?);
  m_json_retn varchar := NULL;
  m_json_args_retn "vxspd"."type_nvarchar2" := NULL;
  m_sql_retn varchar := NULL;
BEGIN
  call "vxspd"."px_route".p_route_json(m_json_args, m_json_argsdetail, m_json_retn, m_json_args_retn, m_sql_retn);
END;
```

## 完整示例

### 基础调用示例

```java
public class ArrayTypeExample {
    public void callStoredProcedure() throws SQLException {
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        try (CallableStatement stmt = connection.prepareCall(sql)) {
            // 设置输入参数
            stmt.setString(1, "{\"action\":\"test\"}");
            stmt.setString(2, "Array parameter as string");  // type_nvarchar2
            
            // 注册输出参数
            stmt.registerOutParameter(3, Types.VARCHAR);  // m_json_retn
            stmt.registerOutParameter(4, Types.VARCHAR);  // m_json_args_retn (type_nvarchar2)
            stmt.registerOutParameter(5, Types.VARCHAR);  // m_sql_retn
            
            // 执行
            stmt.execute();
            
            // 获取结果
            String jsonRetn = stmt.getString(3);
            String arrayRetn = stmt.getString(4);  // 数组类型返回为字符串
            String sqlRetn = stmt.getString(5);
            
            System.out.println("JSON返回: " + jsonRetn);
            System.out.println("数组返回: " + arrayRetn);
            System.out.println("SQL返回: " + sqlRetn);
        }
    }
}
```

### 工具类使用示例

```java
// 使用封装的工具类
KingbaseStoredProcedureUtil util = new KingbaseStoredProcedureUtil(
    "192.168.99.241", 54321, "spd", "vxspd", "vxspd"
);

util.connect();

// 方法1：字符串参数
StoredProcedureResult result1 = util.callRouteJsonProcedure(
    "{\"action\":\"test\"}",
    "Array detail parameter"
);

// 方法2：数组参数
String[] arrayParams = {"param1", "param2", "param3"};
StoredProcedureResult result2 = util.callRouteJsonProcedureWithArray(
    "{\"action\":\"array_test\"}",
    arrayParams
);

util.close();
```

## 注意事项

### 1. 类型兼容性
- `type_nvarchar2` 在 JDBC 中通常被处理为 `VARCHAR` 类型
- 输出参数总是作为字符串返回，即使原始类型是数组

### 2. 数据格式
- 数据库可能对数组元素的格式有特定要求
- 建议先用简单的字符串测试，然后尝试更复杂的格式

### 3. 错误处理
- 如果 Array 对象创建失败，应该有回退机制
- 始终进行适当的异常处理和事务管理

### 4. 性能考虑
- 字符串方式通常比 Array 对象更稳定
- 对于大量数据，考虑分批处理

## 故障排除

### 常见问题

1. **类型不匹配错误**
   ```
   解决方案：使用 setString() 而不是 setArray()
   ```

2. **参数格式错误**
   ```
   解决方案：检查数组元素的格式要求
   ```

3. **空值处理**
   ```java
   if (arrayParam != null) {
       stmt.setString(2, arrayParam);
   } else {
       stmt.setNull(2, Types.VARCHAR);
   }
   ```

### 调试建议

1. 先使用简单的字符串值测试
2. 逐步增加复杂性
3. 检查数据库日志
4. 使用数据库客户端工具验证存储过程

## 最佳实践

1. **优先使用字符串方式**：最稳定和兼容的方法
2. **统一参数格式**：在应用中保持一致的数组参数格式
3. **错误处理**：总是包含适当的异常处理
4. **文档记录**：记录你的应用使用的具体格式
5. **测试验证**：在不同场景下测试数组参数处理

## 示例文件

- `ArrayTypeStoredProcedureExample.java` - 完整的数组类型处理示例
- `KingbaseStoredProcedureUtil.java` - 包含数组类型支持的工具类
- `TestStoredProcedure.java` - 基础存储过程调用示例
