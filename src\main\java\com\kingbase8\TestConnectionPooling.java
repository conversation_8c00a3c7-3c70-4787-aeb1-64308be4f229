package com.kingbase8;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import com.kingbase8.jdbc3.Jdbc3PoolingDataSource;

public class TestConnectionPooling {

    public static void main(String[] args) throws SQLException
    {
        Jdbc3PoolingDataSource ds = new Jdbc3PoolingDataSource();
        ds.setDataSourceName("A Data Source");
        ds.setServerName("localhost");
        ds.setDatabaseName("test");
        ds.setUser("system");
        ds.setPassword("manager");
        ds.setMaxConnections(10);
        ds.setInitialConnections(10);
        ds.setPortNumber(54321);
        Connection conn = ds.getConnection();
        Statement stmt = conn.createStatement();
        stmt.executeUpdate("create table pool (id int)");
        stmt.executeUpdate("insert into pool values (1)");
        ResultSet rs = stmt.executeQuery("select * from pool");
        while(rs.next())
        {
            System.out.println("id:"+ rs.getInt(1));
        }
        stmt.executeUpdate("drop table pool");
        rs.close();
        stmt.close();
        conn.close();
        ds.close();
    }
}
