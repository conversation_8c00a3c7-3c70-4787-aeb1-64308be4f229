package com.kingbase8;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Savepoint;
import java.sql.Statement;

public class TestTransaction {

    protected String Driver = "com.kingbase8.Driver";
    protected static String Url = "*************************************";
    protected Connection connection = null;
    protected Statement statement = null;
    protected void Driver() throws ClassNotFoundException
    {
        Class.forName(Driver);
    }
    protected void connection(String url) throws SQLException,
            InterruptedException
    {
        connection = DriverManager.getConnection(url, "system", "manager");
        if(connection != null)
        {
            System.out.println("connection sucessful!");
        }
        else
        {
            System.out.println("connection fail!");
        }
    }
    protected void statement() throws SQLException
    {
        statement = connection.createStatement();
    }
    protected void table() throws SQLException
    {
        statement.executeUpdate("create table transaction (number int)");
    }
    protected void transactionTest() throws SQLException
    {
        connection.setAutoCommit(false);
        Statement stmt = connection.createStatement();
        Savepoint svpt2 = connection.setSavepoint("SAVEPOINT_2");
        int row1 = stmt.executeUpdate(
                "INSERT INTO transaction (number) VALUES (1)");
        System.out.println(row1);
        Savepoint svpt1 = connection.setSavepoint("SAVEPOINT_1");
        int row2 = stmt.executeUpdate(
                "INSERT INTO transaction (number) VALUES (2)");
        System.out.println(row2);
        connection.rollback(svpt1);
        connection.rollback(svpt2);
        connection.commit();
        ResultSet rs = stmt.executeQuery("select * from transaction");
        while(rs.next())
        {
            System.out.println(rs.getInt(1));
        }
        rs.close();
        stmt.close();
    }
    public static void main(String[] args) throws ClassNotFoundException,
            SQLException, InterruptedException
    {
        TestTransaction test = new TestTransaction();
        test.Driver();
        test.connection(Url);
        test.statement();
        test.table();
        test.transactionTest();
        test.close();
    }
    protected void close() throws SQLException {
        statement.executeUpdate("drop table transaction");
        connection.commit();
        if (statement != null) {
            statement.close();
        }
        if (connection != null) {
            connection.close();
        }
    }
}
