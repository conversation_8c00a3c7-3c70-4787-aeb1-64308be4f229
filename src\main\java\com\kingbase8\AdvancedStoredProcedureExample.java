package com.kingbase8;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 高级存储过程调用示例
 * 演示更复杂的参数处理、错误处理和结果处理
 */
public class AdvancedStoredProcedureExample {
    
    // 数据库连接配置
    private static final String DRIVER = "com.kingbase8.Driver";
    private static final String URL = "*****************************************";
    private static final String USERNAME = "vxspd";
    private static final String PASSWORD = "vxspd";
    
    private Connection connection = null;
    
    /**
     * 初始化数据库连接
     */
    public void initialize() throws ClassNotFoundException, SQLException {
        // 加载驱动
        Class.forName(DRIVER);
        System.out.println("✓ 数据库驱动加载成功");
        
        // 建立连接
        connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
        if (connection != null) {
            System.out.println("✓ 数据库连接成功");
            
            // 设置连接属性
            connection.setAutoCommit(false); // 手动提交事务
            System.out.println("✓ 事务模式设置为手动提交");
        } else {
            throw new SQLException("数据库连接失败");
        }
    }
    
    /**
     * 调用 p_route_json 存储过程的完整示例
     * 包含详细的参数处理和错误处理
     */
    public Map<String, String> callRouteJsonWithFullHandling(String jsonArgs, String jsonArgsDetail) throws SQLException {
        System.out.println("\n=== 调用 p_route_json 存储过程 ===");
        
        Map<String, String> results = new HashMap<>();
        
        // 构建存储过程调用语句
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        try (CallableStatement callableStatement = connection.prepareCall(sql)) {
            
            // 设置输入参数
            if (jsonArgs != null) {
                callableStatement.setString(1, jsonArgs);
            } else {
                callableStatement.setNull(1, Types.VARCHAR);
            }
            
            if (jsonArgsDetail != null) {
                callableStatement.setString(2, jsonArgsDetail);
            } else {
                callableStatement.setNull(2, Types.VARCHAR);
            }
            
            // 注册输出参数
            callableStatement.registerOutParameter(3, Types.VARCHAR);  // m_json_retn
            callableStatement.registerOutParameter(4, Types.VARCHAR);  // m_json_args_retn
            callableStatement.registerOutParameter(5, Types.VARCHAR);  // m_sql_retn
            
            // 显示输入参数
            System.out.println("输入参数:");
            System.out.println("  m_json_args: " + (jsonArgs != null ? jsonArgs : "NULL"));
            System.out.println("  m_json_argsdetail: " + (jsonArgsDetail != null ? jsonArgsDetail : "NULL"));
            
            // 执行存储过程
            long startTime = System.currentTimeMillis();
            System.out.println("\n正在执行存储过程...");
            
            boolean hasResultSet = callableStatement.execute();
            long executionTime = System.currentTimeMillis() - startTime;
            
            System.out.println("存储过程执行完成，耗时: " + executionTime + "ms");
            
            // 获取输出参数
            String jsonRetn = callableStatement.getString(3);
            String jsonArgsRetn = callableStatement.getString(4);
            String sqlRetn = callableStatement.getString(5);
            
            // 检查是否有NULL值
            if (callableStatement.wasNull()) {
                System.out.println("注意: 最后获取的参数值为NULL");
            }
            
            // 存储结果
            results.put("m_json_retn", jsonRetn);
            results.put("m_json_args_retn", jsonArgsRetn);
            results.put("m_sql_retn", sqlRetn);
            
            // 显示输出参数
            System.out.println("\n输出参数:");
            System.out.println("  m_json_retn: " + (jsonRetn != null ? jsonRetn : "NULL"));
            System.out.println("  m_json_args_retn: " + (jsonArgsRetn != null ? jsonArgsRetn : "NULL"));
            System.out.println("  m_sql_retn: " + (sqlRetn != null ? sqlRetn : "NULL"));
            
            // 处理可能的结果集
            if (hasResultSet) {
                System.out.println("\n处理返回的结果集:");
                ResultSet resultSet = callableStatement.getResultSet();
                processResultSet(resultSet);
            }
            
            // 提交事务
            connection.commit();
            System.out.println("✓ 事务提交成功");
            
        } catch (SQLException e) {
            // 回滚事务
            try {
                connection.rollback();
                System.out.println("✗ 事务已回滚");
            } catch (SQLException rollbackEx) {
                System.err.println("回滚事务时发生错误: " + rollbackEx.getMessage());
            }
            
            System.err.println("调用存储过程时发生错误:");
            System.err.println("  错误代码: " + e.getErrorCode());
            System.err.println("  SQL状态: " + e.getSQLState());
            System.err.println("  错误信息: " + e.getMessage());
            throw e;
        }
        
        return results;
    }
    
    /**
     * 使用匿名块调用存储过程并获取结果
     */
    public void callWithAnonymousBlockAndSelect() throws SQLException {
        System.out.println("\n=== 使用匿名块调用并查询结果 ===");
        
        // 创建临时表来存储结果（如果需要）
        String createTempTable = """
            CREATE TEMP TABLE IF NOT EXISTS temp_procedure_results (
                json_retn VARCHAR(4000),
                json_args_retn VARCHAR(4000),
                sql_retn VARCHAR(4000),
                execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """;
        
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTempTable);
            System.out.println("✓ 临时结果表创建成功");
        }
        
        // 执行匿名块
        String anonymousBlock = """
            DECLARE
              "m_json_args" varchar := ?;
              "m_json_argsdetail" "vxspd"."type_nvarchar2" := ?;
              "m_json_retn" varchar := NULL;
              "m_json_args_retn" "vxspd"."type_nvarchar2" := NULL;
              "m_sql_retn" varchar := NULL;
            BEGIN
              call "vxspd"."px_route".p_route_json("m_json_args","m_json_argsdetail","m_json_retn","m_json_args_retn","m_sql_retn");
              
              -- 将结果插入临时表
              INSERT INTO temp_procedure_results (json_retn, json_args_retn, sql_retn)
              VALUES ("m_json_retn", "m_json_args_retn", "m_sql_retn");
              
              COMMIT;
            END;
            """;
        
        try (PreparedStatement pstmt = connection.prepareStatement(anonymousBlock)) {
            pstmt.setString(1, "{\"method\":\"anonymous_block\",\"timestamp\":\"" + System.currentTimeMillis() + "\"}");
            pstmt.setString(2, "匿名块调用测试");
            
            pstmt.execute();
            System.out.println("✓ 匿名块执行成功");
            
            // 查询结果
            String selectResults = "SELECT * FROM temp_procedure_results ORDER BY execution_time DESC LIMIT 1";
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery(selectResults)) {
                
                if (rs.next()) {
                    System.out.println("\n从临时表获取的结果:");
                    System.out.println("  json_retn: " + rs.getString("json_retn"));
                    System.out.println("  json_args_retn: " + rs.getString("json_args_retn"));
                    System.out.println("  sql_retn: " + rs.getString("sql_retn"));
                    System.out.println("  execution_time: " + rs.getTimestamp("execution_time"));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("执行匿名块时发生错误: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 处理结果集的通用方法
     */
    private void processResultSet(ResultSet resultSet) throws SQLException {
        if (resultSet == null) {
            System.out.println("  结果集为空");
            return;
        }
        
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        // 显示列信息
        System.out.println("  结果集包含 " + columnCount + " 列:");
        for (int i = 1; i <= columnCount; i++) {
            System.out.println("    列" + i + ": " + metaData.getColumnName(i) + 
                             " (" + metaData.getColumnTypeName(i) + ")");
        }
        
        // 显示数据
        int rowCount = 0;
        while (resultSet.next()) {
            rowCount++;
            System.out.println("  第" + rowCount + "行:");
            for (int i = 1; i <= columnCount; i++) {
                String value = resultSet.getString(i);
                System.out.println("    " + metaData.getColumnName(i) + ": " + 
                                 (value != null ? value : "NULL"));
            }
        }
        
        if (rowCount == 0) {
            System.out.println("  结果集中没有数据");
        }
    }
    
    /**
     * 批量调用存储过程示例
     */
    public void batchCallProcedure() throws SQLException {
        System.out.println("\n=== 批量调用存储过程示例 ===");
        
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        try (CallableStatement callableStatement = connection.prepareCall(sql)) {
            
            // 准备多组测试数据
            String[][] testData = {
                {"{\"test\":\"batch1\"}", "批量测试1"},
                {"{\"test\":\"batch2\"}", "批量测试2"},
                {"{\"test\":\"batch3\"}", "批量测试3"}
            };
            
            for (int i = 0; i < testData.length; i++) {
                System.out.println("\n--- 批量调用 " + (i + 1) + " ---");
                
                // 设置参数
                callableStatement.setString(1, testData[i][0]);
                callableStatement.setString(2, testData[i][1]);
                callableStatement.registerOutParameter(3, Types.VARCHAR);
                callableStatement.registerOutParameter(4, Types.VARCHAR);
                callableStatement.registerOutParameter(5, Types.VARCHAR);
                
                // 执行
                callableStatement.execute();
                
                // 获取结果
                String result1 = callableStatement.getString(3);
                String result2 = callableStatement.getString(4);
                String result3 = callableStatement.getString(5);
                
                System.out.println("输入: " + testData[i][0] + ", " + testData[i][1]);
                System.out.println("输出: " + result1 + ", " + result2 + ", " + result3);
            }
            
            connection.commit();
            System.out.println("✓ 批量调用完成并提交");
            
        } catch (SQLException e) {
            connection.rollback();
            System.err.println("批量调用失败，事务已回滚: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 关闭资源
     */
    public void cleanup() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("✓ 数据库连接已关闭");
            }
        } catch (SQLException e) {
            System.err.println("关闭连接时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        AdvancedStoredProcedureExample example = new AdvancedStoredProcedureExample();
        
        try {
            // 初始化
            example.initialize();
            
            // 测试1: 完整的存储过程调用
            Map<String, String> results = example.callRouteJsonWithFullHandling(
                "{\"action\":\"test\",\"module\":\"route\",\"timestamp\":\"" + System.currentTimeMillis() + "\"}", 
                "高级测试参数"
            );
            
            // 测试2: 匿名块调用
            example.callWithAnonymousBlockAndSelect();
            
            // 测试3: 批量调用
            example.batchCallProcedure();
            
            System.out.println("\n=== 所有高级测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            example.cleanup();
        }
    }
}
