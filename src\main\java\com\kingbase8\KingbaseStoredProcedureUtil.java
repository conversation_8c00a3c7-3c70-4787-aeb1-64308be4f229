package com.kingbase8;

import java.sql.*;
import java.util.Properties;

/**
 * 人大金仓存储过程调用工具类
 * 提供便捷的存储过程调用方法和连接管理
 */
public class KingbaseStoredProcedureUtil {
    
    private String url;
    private String username;
    private String password;
    private Connection connection;
    
    /**
     * 构造函数
     */
    public KingbaseStoredProcedureUtil(String host, int port, String database, String username, String password) {
        this.url = String.format("jdbc:kingbase8://%s:%d/%s", host, port, database);
        this.username = username;
        this.password = password;
    }
    
    /**
     * 建立数据库连接
     */
    public void connect() throws ClassNotFoundException, SQLException {
        // 加载驱动
        Class.forName("com.kingbase8.Driver");
        
        // 设置连接属性
        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("characterEncoding", "UTF-8");
        props.setProperty("useUnicode", "true");
        
        // 建立连接
        connection = DriverManager.getConnection(url, props);
        connection.setAutoCommit(false); // 默认手动提交事务
        
        System.out.println("数据库连接成功: " + url);
    }
    
    /**
     * Call vxspd.px_route.p_route_json stored procedure
     * Handles type_nvarchar2 (VARCHAR array type) properly
     */
    public StoredProcedureResult callRouteJsonProcedure(String jsonArgs, String jsonArgsDetail) throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("Database connection not established or closed");
        }

        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";

        try (CallableStatement stmt = connection.prepareCall(sql)) {
            // Set input parameters
            stmt.setString(1, jsonArgs);
            // jsonArgsDetail is type_nvarchar2 (VARCHAR array), pass as string
            stmt.setString(2, jsonArgsDetail);

            // Register output parameters
            stmt.registerOutParameter(3, Types.VARCHAR);  // m_json_retn
            stmt.registerOutParameter(4, Types.VARCHAR);  // m_json_args_retn (type_nvarchar2)
            stmt.registerOutParameter(5, Types.VARCHAR);  // m_sql_retn

            // Execute stored procedure
            stmt.execute();

            // Get output parameters
            String jsonRetn = stmt.getString(3);
            String jsonArgsRetn = stmt.getString(4);  // Array type returned as string
            String sqlRetn = stmt.getString(5);

            // Commit transaction
            connection.commit();

            return new StoredProcedureResult(jsonRetn, jsonArgsRetn, sqlRetn);

        } catch (SQLException e) {
            // Rollback transaction
            connection.rollback();
            throw e;
        }
    }

    /**
     * Call stored procedure with Array object for type_nvarchar2
     * Alternative method when you need to pass actual array data
     */
    public StoredProcedureResult callRouteJsonProcedureWithArray(String jsonArgs, String[] arrayDetails) throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("Database connection not established or closed");
        }

        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";

        try (CallableStatement stmt = connection.prepareCall(sql)) {
            // Set input parameter 1
            stmt.setString(1, jsonArgs);

            // Set input parameter 2: Try to create Array for type_nvarchar2
            try {
                Array sqlArray = connection.createArrayOf("varchar", arrayDetails);
                stmt.setArray(2, sqlArray);
            } catch (SQLException arrayEx) {
                // If Array creation fails, convert to comma-separated string
                String arrayAsString = String.join(",", arrayDetails);
                stmt.setString(2, arrayAsString);
                System.out.println("Array creation failed, using string: " + arrayAsString);
            }

            // Register output parameters
            stmt.registerOutParameter(3, Types.VARCHAR);
            stmt.registerOutParameter(4, Types.VARCHAR);
            stmt.registerOutParameter(5, Types.VARCHAR);

            // Execute
            stmt.execute();

            // Get results
            String jsonRetn = stmt.getString(3);
            String jsonArgsRetn = stmt.getString(4);
            String sqlRetn = stmt.getString(5);

            connection.commit();

            return new StoredProcedureResult(jsonRetn, jsonArgsRetn, sqlRetn);

        } catch (SQLException e) {
            connection.rollback();
            throw e;
        }
    }
    
    /**
     * 执行自定义存储过程
     */
    public CallableStatement prepareCall(String sql) throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("数据库连接未建立或已关闭");
        }
        return connection.prepareCall(sql);
    }
    
    /**
     * 执行SQL语句
     */
    public PreparedStatement prepareStatement(String sql) throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("数据库连接未建立或已关闭");
        }
        return connection.prepareStatement(sql);
    }
    
    /**
     * 提交事务
     */
    public void commit() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            connection.commit();
        }
    }
    
    /**
     * 回滚事务
     */
    public void rollback() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            connection.rollback();
        }
    }
    
    /**
     * 关闭连接
     */
    public void close() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            connection.close();
        }
    }
    
    /**
     * 获取连接对象
     */
    public Connection getConnection() {
        return connection;
    }
    
    /**
     * 存储过程结果封装类
     */
    public static class StoredProcedureResult {
        private final String jsonRetn;
        private final String jsonArgsRetn;
        private final String sqlRetn;
        
        public StoredProcedureResult(String jsonRetn, String jsonArgsRetn, String sqlRetn) {
            this.jsonRetn = jsonRetn;
            this.jsonArgsRetn = jsonArgsRetn;
            this.sqlRetn = sqlRetn;
        }
        
        public String getJsonRetn() {
            return jsonRetn;
        }
        
        public String getJsonArgsRetn() {
            return jsonArgsRetn;
        }
        
        public String getSqlRetn() {
            return sqlRetn;
        }
        
        @Override
        public String toString() {
            return String.format("StoredProcedureResult{jsonRetn='%s', jsonArgsRetn='%s', sqlRetn='%s'}", 
                               jsonRetn, jsonArgsRetn, sqlRetn);
        }
    }
    
    /**
     * Usage examples demonstrating array type handling
     */
    public static void main(String[] args) {
        KingbaseStoredProcedureUtil util = new KingbaseStoredProcedureUtil(
            "192.168.99.241", 54321, "spd", "vxspd", "vxspd"
        );

        try {
            // Connect to database
            util.connect();

            // Test 1: Call stored procedure with string parameter (type_nvarchar2 as string)
            System.out.println("=== Test 1: String parameter for array type ===");
            StoredProcedureResult result1 = util.callRouteJsonProcedure(
                "{\"action\":\"test\",\"data\":\"sample\"}",
                "Array detail parameter as string"
            );

            System.out.println("Stored procedure result:");
            System.out.println("  JSON return: " + result1.getJsonRetn());
            System.out.println("  Args return (array type): " + result1.getJsonArgsRetn());
            System.out.println("  SQL return: " + result1.getSqlRetn());

            // Test 2: Call with array object
            System.out.println("\n=== Test 2: Array object for type_nvarchar2 ===");
            String[] arrayParams = {"param1", "param2", "param3"};
            StoredProcedureResult result2 = util.callRouteJsonProcedureWithArray(
                "{\"action\":\"array_test\",\"elements\":" + arrayParams.length + "}",
                arrayParams
            );

            System.out.println("Array parameter result:");
            System.out.println("  JSON return: " + result2.getJsonRetn());
            System.out.println("  Args return: " + result2.getJsonArgsRetn());
            System.out.println("  SQL return: " + result2.getSqlRetn());

            // Test 3: Custom call with manual parameter handling
            System.out.println("\n=== Test 3: Custom call ===");
            String customSql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
            try (CallableStatement stmt = util.prepareCall(customSql)) {
                stmt.setString(1, "{\"custom\":\"call\",\"timestamp\":\"" + System.currentTimeMillis() + "\"}");
                // For type_nvarchar2, you can pass various formats:
                stmt.setString(2, "Custom array parameter");  // Single string
                // stmt.setString(2, "val1,val2,val3");  // Comma-separated
                // stmt.setString(2, "{\"array\":[\"val1\",\"val2\"]}");  // JSON format

                stmt.registerOutParameter(3, Types.VARCHAR);
                stmt.registerOutParameter(4, Types.VARCHAR);
                stmt.registerOutParameter(5, Types.VARCHAR);

                stmt.execute();

                System.out.println("Custom call result:");
                System.out.println("  Output 1: " + stmt.getString(3));
                System.out.println("  Output 2 (array type): " + stmt.getString(4));
                System.out.println("  Output 3: " + stmt.getString(5));

                util.commit();
            }

            System.out.println("\n=== All tests completed successfully ===");

        } catch (Exception e) {
            System.err.println("Operation failed: " + e.getMessage());
            e.printStackTrace();
            try {
                util.rollback();
            } catch (SQLException rollbackEx) {
                System.err.println("Rollback failed: " + rollbackEx.getMessage());
            }
        } finally {
            try {
                util.close();
            } catch (SQLException e) {
                System.err.println("Failed to close connection: " + e.getMessage());
            }
        }
    }
}
