package com.kingbase8;

import java.sql.*;
import java.util.Properties;

/**
 * 人大金仓存储过程调用工具类
 * 提供便捷的存储过程调用方法和连接管理
 */
public class KingbaseStoredProcedureUtil {
    
    private String url;
    private String username;
    private String password;
    private Connection connection;
    
    /**
     * 构造函数
     */
    public KingbaseStoredProcedureUtil(String host, int port, String database, String username, String password) {
        this.url = String.format("jdbc:kingbase8://%s:%d/%s", host, port, database);
        this.username = username;
        this.password = password;
    }
    
    /**
     * 建立数据库连接
     */
    public void connect() throws ClassNotFoundException, SQLException {
        // 加载驱动
        Class.forName("com.kingbase8.Driver");
        
        // 设置连接属性
        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("characterEncoding", "UTF-8");
        props.setProperty("useUnicode", "true");
        
        // 建立连接
        connection = DriverManager.getConnection(url, props);
        connection.setAutoCommit(false); // 默认手动提交事务
        
        System.out.println("数据库连接成功: " + url);
    }
    
    /**
     * 调用 vxspd.px_route.p_route_json 存储过程的便捷方法
     */
    public StoredProcedureResult callRouteJsonProcedure(String jsonArgs, String jsonArgsDetail) throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("数据库连接未建立或已关闭");
        }
        
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        try (CallableStatement stmt = connection.prepareCall(sql)) {
            // 设置输入参数
            stmt.setString(1, jsonArgs);
            stmt.setString(2, jsonArgsDetail);
            
            // 注册输出参数
            stmt.registerOutParameter(3, Types.VARCHAR);  // m_json_retn
            stmt.registerOutParameter(4, Types.VARCHAR);  // m_json_args_retn
            stmt.registerOutParameter(5, Types.VARCHAR);  // m_sql_retn
            
            // 执行存储过程
            stmt.execute();
            
            // 获取输出参数
            String jsonRetn = stmt.getString(3);
            String jsonArgsRetn = stmt.getString(4);
            String sqlRetn = stmt.getString(5);
            
            // 提交事务
            connection.commit();
            
            return new StoredProcedureResult(jsonRetn, jsonArgsRetn, sqlRetn);
            
        } catch (SQLException e) {
            // 回滚事务
            connection.rollback();
            throw e;
        }
    }
    
    /**
     * 执行自定义存储过程
     */
    public CallableStatement prepareCall(String sql) throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("数据库连接未建立或已关闭");
        }
        return connection.prepareCall(sql);
    }
    
    /**
     * 执行SQL语句
     */
    public PreparedStatement prepareStatement(String sql) throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("数据库连接未建立或已关闭");
        }
        return connection.prepareStatement(sql);
    }
    
    /**
     * 提交事务
     */
    public void commit() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            connection.commit();
        }
    }
    
    /**
     * 回滚事务
     */
    public void rollback() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            connection.rollback();
        }
    }
    
    /**
     * 关闭连接
     */
    public void close() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            connection.close();
        }
    }
    
    /**
     * 获取连接对象
     */
    public Connection getConnection() {
        return connection;
    }
    
    /**
     * 存储过程结果封装类
     */
    public static class StoredProcedureResult {
        private final String jsonRetn;
        private final String jsonArgsRetn;
        private final String sqlRetn;
        
        public StoredProcedureResult(String jsonRetn, String jsonArgsRetn, String sqlRetn) {
            this.jsonRetn = jsonRetn;
            this.jsonArgsRetn = jsonArgsRetn;
            this.sqlRetn = sqlRetn;
        }
        
        public String getJsonRetn() {
            return jsonRetn;
        }
        
        public String getJsonArgsRetn() {
            return jsonArgsRetn;
        }
        
        public String getSqlRetn() {
            return sqlRetn;
        }
        
        @Override
        public String toString() {
            return String.format("StoredProcedureResult{jsonRetn='%s', jsonArgsRetn='%s', sqlRetn='%s'}", 
                               jsonRetn, jsonArgsRetn, sqlRetn);
        }
    }
    
    /**
     * 使用示例
     */
    public static void main(String[] args) {
        KingbaseStoredProcedureUtil util = new KingbaseStoredProcedureUtil(
            "192.168.99.241", 54321, "spd", "vxspd", "vxspd"
        );
        
        try {
            // 连接数据库
            util.connect();
            
            // 调用存储过程
            StoredProcedureResult result = util.callRouteJsonProcedure(
                "{\"action\":\"test\",\"data\":\"sample\"}",
                "测试详细参数"
            );
            
            // 显示结果
            System.out.println("存储过程调用结果:");
            System.out.println("  JSON返回值: " + result.getJsonRetn());
            System.out.println("  参数返回值: " + result.getJsonArgsRetn());
            System.out.println("  SQL返回值: " + result.getSqlRetn());
            
            // 自定义调用示例
            String customSql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
            try (CallableStatement stmt = util.prepareCall(customSql)) {
                stmt.setString(1, "{\"custom\":\"call\"}");
                stmt.setString(2, "自定义调用");
                stmt.registerOutParameter(3, Types.VARCHAR);
                stmt.registerOutParameter(4, Types.VARCHAR);
                stmt.registerOutParameter(5, Types.VARCHAR);
                
                stmt.execute();
                
                System.out.println("\n自定义调用结果:");
                System.out.println("  输出1: " + stmt.getString(3));
                System.out.println("  输出2: " + stmt.getString(4));
                System.out.println("  输出3: " + stmt.getString(5));
                
                util.commit();
            }
            
        } catch (Exception e) {
            System.err.println("操作失败: " + e.getMessage());
            e.printStackTrace();
            try {
                util.rollback();
            } catch (SQLException rollbackEx) {
                System.err.println("回滚失败: " + rollbackEx.getMessage());
            }
        } finally {
            try {
                util.close();
            } catch (SQLException e) {
                System.err.println("关闭连接失败: " + e.getMessage());
            }
        }
    }
}
