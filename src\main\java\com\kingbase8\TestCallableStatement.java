package com.kingbase8;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
public class TestCallableStatement {

    protected String Driver = "com.kingbase8.Driver";
    protected static String Url = "*************************************";
    protected Connection connection = null;
    protected Statement statement = null;
    protected void Driver() throws ClassNotFoundException
    {
        Class.forName(Driver);
    }
    protected void connection(String url) throws SQLException,
            InterruptedException
    {
        connection = DriverManager.getConnection(url, "system", "manager");
        if(connection != null)
        {
            System.out.println("connection sucessful!");
        }
        else
        {
            System.out.println("connection fail!");
        }
    }
    protected void statement() throws SQLException
    {
        statement = connection.createStatement();
    }
    protected void table() throws SQLException
    {
        statement.executeUpdate("create table testrs (id integer primary " +
                "key)");
        for(int i = 0; i < 5 ; i++)
        {
            statement.executeUpdate("insert into testrs values (" + i + ")");
        }
    }
    public static void main(String[] args) throws ClassNotFoundException,
            SQLException, InterruptedException
    {
        TestCallableStatement test = new TestCallableStatement();
        test.Driver();
        test.connection(Url);
        test.statement();
        test.table();
        test.callableStatement();
        test.close();
    }
    public void callableStatement() throws SQLException
    {
        statement.execute("CREATE OR REPLACE INTERNAL FUNCTION "
                + "testssys_getRefcursor() RETURNS refcursor AS '"
                + "declare v_resset refcursor; "
                + "begin "
                + "open v_resset for select id from testrs order by id; "
                + "return v_resset; "
                + "end;' LANGUAGE plsql;");
        statement.execute("create or replace procedure "
                + "testssys_demo(v_temp out refcursor) as \n"
                + "begin \n"
                + "open v_temp for select id from testrs; \n"
                + "end; ");
        connection.setAutoCommit(false);
        CallableStatement call = connection.prepareCall(
                "{ ? = call testssys_getRefcursor() }");
        call.registerOutParameter(1, Types.REF_CURSOR);
        call.execute();
        ResultSet rs = (ResultSet) call.getObject(1);
        System.out.println("testssys_getRefcursor:");
        while(rs.next()) {
            System.out.println(rs.getInt(1));
        }
        CallableStatement call2 = connection.prepareCall(
                "{call testssys_demo (?)}");
        call2.registerOutParameter(1, Types.REF_CURSOR);
        call2.execute();
        ResultSet rs2 = (ResultSet) call2.getObject(1);
        System.out.println("testssys_demo:");
        while(rs2.next()){
            System.out.println(rs2.getInt(1));
        }
        connection.commit();
        connection.setAutoCommit(true);
    }
    protected void close() throws SQLException
    {
        statement.executeUpdate("drop table testrs");
        if(statement != null)
        {
            statement.close();
        }
        if(connection != null)
        {
            connection.close();
        }
    }
}
