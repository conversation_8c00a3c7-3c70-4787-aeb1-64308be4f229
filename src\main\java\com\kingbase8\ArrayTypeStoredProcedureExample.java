package com.kingbase8;

import java.sql.*;

/**
 * Kingbase Array Type Stored Procedure Example
 * Demonstrates how to handle vxspd.type_nvarchar2 (VARCHAR array type) in stored procedures
 */
public class ArrayTypeStoredProcedureExample {
    
    // Database connection configuration
    private static final String DRIVER = "com.kingbase8.Driver";
    private static final String URL = "*****************************************";
    private static final String USERNAME = "vxspd";
    private static final String PASSWORD = "vxspd";
    
    private Connection connection = null;
    
    /**
     * Initialize database connection
     */
    public void initialize() throws ClassNotFoundException, SQLException {
        Class.forName(DRIVER);
        System.out.println("Database driver loaded successfully");
        
        connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
        if (connection != null) {
            System.out.println("Database connection successful");
            connection.setAutoCommit(false);
            System.out.println("Transaction mode set to manual commit");
        } else {
            throw new SQLException("Database connection failed");
        }
    }
    
    /**
     * Call p_route_json stored procedure with proper array type handling
     * type_nvarchar2 is a variable array type with varchar subtype
     */
    public void callRouteJsonWithArrayType() throws SQLException {
        System.out.println("\n=== Calling p_route_json with array type handling ===");
        
        // Prepare input parameters
        String jsonArgs = "{\"action\":\"test\",\"module\":\"route\",\"timestamp\":\"" + System.currentTimeMillis() + "\"}";
        
        // For type_nvarchar2 (VARCHAR array), we can pass:
        // 1. A single string value
        // 2. An Array object created from the connection
        // 3. A comma-separated string that the database can parse
        
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        try (CallableStatement stmt = connection.prepareCall(sql)) {
            
            // Set input parameter 1: m_json_args (varchar)
            stmt.setString(1, jsonArgs);
            System.out.println("Input m_json_args: " + jsonArgs);
            
            // Set input parameter 2: m_json_argsdetail (type_nvarchar2 - VARCHAR array)
            // Method 1: Pass as single string (most common approach)
            String arrayDetailString = "Detail parameter for array type";
            stmt.setString(2, arrayDetailString);
            System.out.println("Input m_json_argsdetail (as string): " + arrayDetailString);
            
            // Register output parameters
            stmt.registerOutParameter(3, Types.VARCHAR);    // m_json_retn
            stmt.registerOutParameter(4, Types.VARCHAR);    // m_json_args_retn (type_nvarchar2)
            stmt.registerOutParameter(5, Types.VARCHAR);    // m_sql_retn
            
            // Execute the stored procedure
            long startTime = System.currentTimeMillis();
            System.out.println("\nExecuting stored procedure...");
            stmt.execute();
            long executionTime = System.currentTimeMillis() - startTime;
            
            System.out.println("Stored procedure executed successfully in " + executionTime + "ms");
            
            // Get output parameters
            String jsonRetn = stmt.getString(3);
            String jsonArgsRetn = stmt.getString(4);  // This is type_nvarchar2 but returned as string
            String sqlRetn = stmt.getString(5);
            
            // Display results
            System.out.println("\nOutput parameters:");
            System.out.println("  m_json_retn: " + (jsonRetn != null ? jsonRetn : "NULL"));
            System.out.println("  m_json_args_retn (array type): " + (jsonArgsRetn != null ? jsonArgsRetn : "NULL"));
            System.out.println("  m_sql_retn: " + (sqlRetn != null ? sqlRetn : "NULL"));
            
            // Commit transaction
            connection.commit();
            System.out.println("Transaction committed successfully");
            
        } catch (SQLException e) {
            connection.rollback();
            System.err.println("Error calling stored procedure: " + e.getMessage());
            System.err.println("Error code: " + e.getErrorCode());
            System.err.println("SQL state: " + e.getSQLState());
            throw e;
        }
    }
    
    /**
     * Alternative method: Create and use Array object for type_nvarchar2
     */
    public void callRouteJsonWithArrayObject() throws SQLException {
        System.out.println("\n=== Calling p_route_json with Array object ===");
        
        String jsonArgs = "{\"method\":\"array_object\",\"timestamp\":\"" + System.currentTimeMillis() + "\"}";
        
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        try (CallableStatement stmt = connection.prepareCall(sql)) {
            
            // Set input parameter 1
            stmt.setString(1, jsonArgs);
            
            // Set input parameter 2: Try to create Array object for type_nvarchar2
            try {
                // Create array of strings for the VARCHAR array type
                String[] arrayElements = {"element1", "element2", "element3"};
                Array sqlArray = connection.createArrayOf("varchar", arrayElements);
                
                stmt.setArray(2, sqlArray);
                System.out.println("Input m_json_argsdetail (as Array): " + java.util.Arrays.toString(arrayElements));
                
            } catch (SQLException arrayEx) {
                // If Array creation fails, fall back to string
                System.out.println("Array creation failed, falling back to string: " + arrayEx.getMessage());
                stmt.setString(2, "Fallback string for array parameter");
            }
            
            // Register output parameters
            stmt.registerOutParameter(3, Types.VARCHAR);
            stmt.registerOutParameter(4, Types.VARCHAR);
            stmt.registerOutParameter(5, Types.VARCHAR);
            
            // Execute
            stmt.execute();
            
            // Get results
            String jsonRetn = stmt.getString(3);
            String jsonArgsRetn = stmt.getString(4);
            String sqlRetn = stmt.getString(5);
            
            System.out.println("\nOutput parameters:");
            System.out.println("  m_json_retn: " + (jsonRetn != null ? jsonRetn : "NULL"));
            System.out.println("  m_json_args_retn: " + (jsonArgsRetn != null ? jsonArgsRetn : "NULL"));
            System.out.println("  m_sql_retn: " + (sqlRetn != null ? sqlRetn : "NULL"));
            
            connection.commit();
            
        } catch (SQLException e) {
            connection.rollback();
            System.err.println("Error with Array object method: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Use anonymous block with proper array type handling
     */
    public void callWithAnonymousBlockArrayType() throws SQLException {
        System.out.println("\n=== Using anonymous block with array type ===");
        
        // Anonymous block that properly declares the array type
        String anonymousBlock = 
            "DECLARE " +
            "  m_json_args varchar := ?; " +
            "  m_json_argsdetail \"vxspd\".\"type_nvarchar2\" := \"vxspd\".\"type_nvarchar2\"(?); " +
            "  m_json_retn varchar := NULL; " +
            "  m_json_args_retn \"vxspd\".\"type_nvarchar2\" := NULL; " +
            "  m_sql_retn varchar := NULL; " +
            "BEGIN " +
            "  call \"vxspd\".\"px_route\".p_route_json(m_json_args, m_json_argsdetail, m_json_retn, m_json_args_retn, m_sql_retn); " +
            "  -- Results are stored in the variables but cannot be directly returned from anonymous block " +
            "END;";
        
        try (PreparedStatement pstmt = connection.prepareStatement(anonymousBlock)) {
            
            String jsonArgs = "{\"method\":\"anonymous_block_array\",\"timestamp\":\"" + System.currentTimeMillis() + "\"}";
            String arrayDetail = "Array detail parameter";
            
            pstmt.setString(1, jsonArgs);
            pstmt.setString(2, arrayDetail);
            
            System.out.println("Executing anonymous block with array type constructor...");
            System.out.println("Input parameters:");
            System.out.println("  m_json_args: " + jsonArgs);
            System.out.println("  m_json_argsdetail: " + arrayDetail);
            
            pstmt.execute();
            connection.commit();
            
            System.out.println("Anonymous block executed successfully");
            System.out.println("Note: Results are processed within the block but not returned directly");
            
        } catch (SQLException e) {
            connection.rollback();
            System.err.println("Error executing anonymous block: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Test different ways to handle the array type parameter
     */
    public void testArrayTypeHandling() throws SQLException {
        System.out.println("\n=== Testing different array type handling methods ===");
        
        String[] testValues = {
            "Single string value",
            "Value1,Value2,Value3",  // Comma-separated
            "{\"array\":[\"val1\",\"val2\",\"val3\"]}",  // JSON array format
            "Array element with special chars: !@#$%^&*()"
        };
        
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        for (int i = 0; i < testValues.length; i++) {
            System.out.println("\n--- Test " + (i + 1) + " ---");
            
            try (CallableStatement stmt = connection.prepareCall(sql)) {
                
                String jsonArgs = "{\"test_case\":" + (i + 1) + ",\"timestamp\":\"" + System.currentTimeMillis() + "\"}";
                
                stmt.setString(1, jsonArgs);
                stmt.setString(2, testValues[i]);
                
                stmt.registerOutParameter(3, Types.VARCHAR);
                stmt.registerOutParameter(4, Types.VARCHAR);
                stmt.registerOutParameter(5, Types.VARCHAR);
                
                System.out.println("Input array parameter: " + testValues[i]);
                
                stmt.execute();
                
                String result1 = stmt.getString(3);
                String result2 = stmt.getString(4);
                String result3 = stmt.getString(5);
                
                System.out.println("Results:");
                System.out.println("  Output 1: " + (result1 != null ? result1 : "NULL"));
                System.out.println("  Output 2: " + (result2 != null ? result2 : "NULL"));
                System.out.println("  Output 3: " + (result3 != null ? result3 : "NULL"));
                
                connection.commit();
                
            } catch (SQLException e) {
                connection.rollback();
                System.err.println("Test " + (i + 1) + " failed: " + e.getMessage());
            }
        }
    }
    
    /**
     * Clean up resources
     */
    public void cleanup() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("Database connection closed");
            }
        } catch (SQLException e) {
            System.err.println("Error closing connection: " + e.getMessage());
        }
    }
    
    /**
     * Main method
     */
    public static void main(String[] args) {
        ArrayTypeStoredProcedureExample example = new ArrayTypeStoredProcedureExample();
        
        try {
            // Initialize connection
            example.initialize();
            
            // Test 1: Basic array type handling
            example.callRouteJsonWithArrayType();
            
            // Test 2: Array object approach
            example.callRouteJsonWithArrayObject();
            
            // Test 3: Anonymous block with array type constructor
            example.callWithAnonymousBlockArrayType();
            
            // Test 4: Different array parameter formats
            example.testArrayTypeHandling();
            
            System.out.println("\n=== All array type tests completed ===");
            
        } catch (Exception e) {
            System.err.println("Program execution failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            example.cleanup();
        }
    }
}
