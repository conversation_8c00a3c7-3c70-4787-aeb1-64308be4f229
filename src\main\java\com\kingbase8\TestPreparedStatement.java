package com.kingbase8;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ParameterMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class TestPreparedStatement {

    protected String Driver = "com.kingbase8.Driver";
    protected static String Url = "*************************************";
    protected Connection connection = null;
    protected Statement statement = null;
    protected enumn[] employee = new enumn[5];
    protected void Driver() throws ClassNotFoundException
    {
        Class.forName(Driver);
    }
    public static void main(String[] args) throws SQLException,
            ClassNotFoundException, InterruptedException
    {
        TestPreparedStatement test = new TestPreparedStatement();
        test.initEmployee();
        test.Driver();
        test.connection(Url);
        test.statement();
        test.table();
        test.preparedStatement();
        test.getParameterMetaData();
        test.close();
    }
    protected void initEmployee()
    {
        for(int i = 0; i < employee.length; i++)
        {
            employee[i] = new enumn();
            employee[i].id = i;
            employee[i].salary = new BigDecimal("100." + i);
        }
    }
    protected void connection(String url) throws SQLException,
            InterruptedException
    {
        connection = DriverManager.getConnection(url, "system", "manager");
        if(connection != null)
        {
            System.out.println("connection sucessful!");
        }
        else
        {
            System.out.println("connection fail!");
        }
    }
    protected void statement() throws SQLException
    {
        statement = connection.createStatement();
    }
    protected void table() throws SQLException
    {
        statement.executeUpdate("create table employees (ID int not null " +
                "primary key, SALARY numeric(9,5))");
        for(int i = 0; i < 5 ; i++)
        {
            statement.executeUpdate("insert into employees values (" + i +
                    "," + "100.10" + ")");
        }
    }
    protected void preparedStatement() throws SQLException
    {
        PreparedStatement preparedStatement = connection.prepareStatement(
                "UPDATE employees SET SALARY = ? WHERE ID = ?");
        for(int i = 0; i < employee.length; i++)
        {
            preparedStatement.setBigDecimal(1, employee[i].salary);
            preparedStatement.setInt(2, employee[i].id);
            preparedStatement.executeUpdate();
        }
        ResultSet rs = statement.executeQuery("select SALARY from employees");
        while(rs.next())
        {
            System.out.println(rs.getBigDecimal(1));
        }
        rs.close();
        preparedStatement.close();
    }
    protected void getParameterMetaData() throws SQLException
    {
        statement.executeUpdate("delete from employees");
        PreparedStatement preparedStatement = connection.prepareStatement(
                "insert into employees (ID, SALARY) values (?,?)");
        ParameterMetaData pmd = preparedStatement.getParameterMetaData();
        int parameterCount = pmd.getParameterCount();
        System.out.println(parameterCount);
        preparedStatement.close();
    }
    protected void close() throws SQLException
    {
        statement.executeUpdate("drop table employees");
        if(statement != null)
        {
            statement.close();
        }
        if(connection != null)
        {
            connection.close();
        }
    }
}

class enumn
{
    BigDecimal salary;
    int id;
}
