package com.kingbase8;

import java.sql.*;

/**
 * Kingbase stored procedure call example
 * Demonstrates how to call stored procedures with custom type parameters using JDBC
 */
public class TestStoredProcedure {

    // Database connection configuration
    private static final String DRIVER = "com.kingbase8.Driver";
    private static final String URL = "*****************************************";
    private static final String USERNAME = "vxspd";
    private static final String PASSWORD = "vxspd";

    private Connection connection = null;

    /**
     * Load database driver
     */
    private void loadDriver() throws ClassNotFoundException {
        Class.forName(DRIVER);
        System.out.println("Database driver loaded successfully");
    }

    /**
     * Establish database connection
     */
    private void connect() throws SQLException {
        connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
        if (connection != null) {
            System.out.println("Database connection successful!");
            System.out.println("Connection URL: " + URL);
            System.out.println("Username: " + USERNAME);
        } else {
            throw new SQLException("Database connection failed!");
        }
    }
    
    /**
     * 调用存储过程 vxspd.px_route.p_route_json
     * 该方法演示了如何处理包含自定义类型的存储过程调用
     */
    public void callRouteJsonProcedure() throws SQLException {
        System.out.println("\n=== 开始调用存储过程 vxspd.px_route.p_route_json ===");
        
        // 准备输入参数
        String jsonArgs = "{\"action\":\"test\",\"data\":\"sample data\"}";
        String jsonArgsDetail = "详细参数信息";
        
        // 使用CallableStatement调用存储过程
        String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
        
        try (CallableStatement callableStatement = connection.prepareCall(sql)) {
            
            // 设置输入参数
            callableStatement.setString(1, jsonArgs);           // m_json_args (输入)
            callableStatement.setString(2, jsonArgsDetail);     // m_json_argsdetail (输入)
            
            // 注册输出参数
            callableStatement.registerOutParameter(3, Types.VARCHAR);  // m_json_retn (输出)
            callableStatement.registerOutParameter(4, Types.VARCHAR);  // m_json_args_retn (输出) 
            callableStatement.registerOutParameter(5, Types.VARCHAR);  // m_sql_retn (输出)
            
            System.out.println("输入参数:");
            System.out.println("  m_json_args: " + jsonArgs);
            System.out.println("  m_json_argsdetail: " + jsonArgsDetail);
            
            // 执行存储过程
            System.out.println("\n正在执行存储过程...");
            callableStatement.execute();
            
            // 获取输出参数
            String jsonRetn = callableStatement.getString(3);
            String jsonArgsRetn = callableStatement.getString(4);
            String sqlRetn = callableStatement.getString(5);
            
            // 显示结果
            System.out.println("\n存储过程执行完成，输出参数:");
            System.out.println("  m_json_retn: " + (jsonRetn != null ? jsonRetn : "NULL"));
            System.out.println("  m_json_args_retn: " + (jsonArgsRetn != null ? jsonArgsRetn : "NULL"));
            System.out.println("  m_sql_retn: " + (sqlRetn != null ? sqlRetn : "NULL"));
            
        } catch (SQLException e) {
            System.err.println("调用存储过程时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 使用匿名块的方式调用存储过程（类似您提供的SQL语句）
     */
    public void callProcedureWithAnonymousBlock() throws SQLException {
        System.out.println("\n=== 使用匿名块调用存储过程 ===");
        
        String sql = """
            DECLARE
              "m_json_args" varchar := ?;
              "m_json_argsdetail" "vxspd"."type_nvarchar2" := ?;
              "m_json_retn" varchar := NULL;
              "m_json_args_retn" "vxspd"."type_nvarchar2" := NULL;
              "m_sql_retn" varchar := NULL;
            BEGIN
              call "vxspd"."px_route".p_route_json("m_json_args","m_json_argsdetail","m_json_retn","m_json_args_retn","m_sql_retn");
              -- 这里可以添加更多的业务逻辑
              -- 注意：在匿名块中无法直接返回结果集，需要使用其他方式获取结果
            END;
            """;
        
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            // 设置输入参数
            preparedStatement.setString(1, "{\"method\":\"anonymous_block\",\"timestamp\":\"" + System.currentTimeMillis() + "\"}");
            preparedStatement.setString(2, "通过匿名块调用的详细参数");
            
            System.out.println("正在执行匿名块...");
            preparedStatement.execute();
            System.out.println("匿名块执行完成");
            
        } catch (SQLException e) {
            System.err.println("执行匿名块时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 演示带有结果集返回的存储过程调用
     */
    public void callProcedureWithResultSet() throws SQLException {
        System.out.println("\n=== 调用带结果集的存储过程示例 ===");
        
        // 这是一个示例，展示如何处理返回结果集的存储过程
        String sql = "{ ? = call some_function_returning_cursor() }";
        
        try (CallableStatement callableStatement = connection.prepareCall(sql)) {
            // 注册返回的游标参数
            callableStatement.registerOutParameter(1, Types.REF_CURSOR);
            
            // 执行存储过程
            callableStatement.execute();
            
            // 获取结果集
            ResultSet resultSet = (ResultSet) callableStatement.getObject(1);
            if (resultSet != null) {
                System.out.println("处理返回的结果集:");
                while (resultSet.next()) {
                    // 根据实际的结果集结构处理数据
                    System.out.println("  数据行: " + resultSet.getString(1));
                }
                resultSet.close();
            }
            
        } catch (SQLException e) {
            System.out.println("注意: 这是一个示例方法，实际的存储过程可能不存在");
            System.out.println("错误信息: " + e.getMessage());
        }
    }
    
    /**
     * 关闭数据库连接
     */
    private void closeConnection() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            connection.close();
            System.out.println("\n数据库连接已关闭");
        }
    }
    
    /**
     * 主方法 - 程序入口
     */
    public static void main(String[] args) {
        TestStoredProcedure test = new TestStoredProcedure();
        
        try {
            // 1. 加载驱动
            test.loadDriver();
            
            // 2. 建立连接
            test.connect();
            
            // 3. 调用存储过程（推荐方式）
            test.callRouteJsonProcedure();
            
            // 4. 使用匿名块调用（可选）
            test.callProcedureWithAnonymousBlock();
            
            // 5. 演示结果集处理（示例）
            test.callProcedureWithResultSet();
            
            System.out.println("\n=== 所有测试完成 ===");
            
        } catch (ClassNotFoundException e) {
            System.err.println("数据库驱动加载失败: " + e.getMessage());
            e.printStackTrace();
        } catch (SQLException e) {
            System.err.println("数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 6. 关闭连接
            try {
                test.closeConnection();
            } catch (SQLException e) {
                System.err.println("关闭数据库连接时发生错误: " + e.getMessage());
            }
        }
    }
}
