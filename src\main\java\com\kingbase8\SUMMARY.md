# 人大金仓存储过程调用完整解决方案

## 项目概述

本项目提供了使用 JDBC 调用人大金仓数据库存储过程的完整解决方案，特别针对包含自定义数组类型 `type_nvarchar2` 的存储过程 `vxspd.px_route.p_route_json`。

## 核心特性

✅ **完整的数组类型支持** - 正确处理 `vxspd.type_nvarchar2` (VARCHAR 数组类型)  
✅ **多种调用方式** - CallableStatement、匿名块、工具类封装  
✅ **错误处理和事务管理** - 完善的异常处理和事务回滚机制  
✅ **连接测试工具** - 快速验证数据库连接和存储过程可用性  
✅ **详细文档和示例** - 包含完整的使用说明和最佳实践  

## 文件结构

```
src/main/java/com/kingbase8/
├── QuickConnectionTest.java              # 🔧 连接测试工具
├── ArrayTypeStoredProcedureExample.java  # ⭐ 数组类型处理示例（推荐）
├── KingbaseStoredProcedureUtil.java      # 🛠️ 工具类（最简单）
├── TestStoredProcedure.java              # 📚 基础示例
├── AdvancedStoredProcedureExample.java   # 🚀 高级示例
├── README_StoredProcedure.md             # 📖 主要文档
├── ArrayTypeHandling_README.md           # 📋 数组类型详细说明
└── SUMMARY.md                            # 📄 本文件
```

## 快速开始

### 1. 验证环境
```bash
# 编译测试
javac -cp "lib/kingbase8-9.0.0.jar" -encoding UTF-8 src/main/java/com/kingbase8/*.java

# 连接测试
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.QuickConnectionTest
```

### 2. 运行示例
```bash
# 数组类型处理示例（推荐）
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.ArrayTypeStoredProcedureExample

# 工具类示例（最简单）
java -cp "lib/kingbase8-9.0.0.jar:src/main/java" com.kingbase8.KingbaseStoredProcedureUtil
```

## 核心技术要点

### type_nvarchar2 数组类型处理

`vxspd.type_nvarchar2` 是可变 VARCHAR 数组类型，处理方式：

```java
// 推荐方式：作为字符串传递
stmt.setString(2, "Array parameter as string");

// 备选方式：使用 Array 对象
String[] elements = {"val1", "val2", "val3"};
Array sqlArray = connection.createArrayOf("varchar", elements);
stmt.setArray(2, sqlArray);

// 支持多种格式
stmt.setString(2, "value1,value2,value3");  // 逗号分隔
stmt.setString(2, "{\"array\":[\"val1\",\"val2\"]}");  // JSON格式
```

### 存储过程调用模式

```java
// 标准 CallableStatement 调用
String sql = "{ call \"vxspd\".\"px_route\".p_route_json(?, ?, ?, ?, ?) }";
CallableStatement stmt = connection.prepareCall(sql);

// 设置输入参数
stmt.setString(1, jsonArgs);                    // varchar
stmt.setString(2, arrayDetail);                 // type_nvarchar2

// 注册输出参数
stmt.registerOutParameter(3, Types.VARCHAR);    // m_json_retn
stmt.registerOutParameter(4, Types.VARCHAR);    // m_json_args_retn
stmt.registerOutParameter(5, Types.VARCHAR);    // m_sql_retn

// 执行并获取结果
stmt.execute();
String result1 = stmt.getString(3);
String result2 = stmt.getString(4);  // 数组类型返回为字符串
String result3 = stmt.getString(5);
```

## 推荐使用流程

### 对于新用户
1. **QuickConnectionTest** - 验证连接和环境
2. **KingbaseStoredProcedureUtil** - 使用工具类快速实现
3. **ArrayTypeStoredProcedureExample** - 了解数组类型处理细节

### 对于高级用户
1. **ArrayTypeStoredProcedureExample** - 学习完整的数组类型处理
2. **AdvancedStoredProcedureExample** - 了解高级特性和优化
3. 根据需求自定义实现

## 配置信息

```java
// 数据库连接配置
String host = "**************";
int port = 54321;
String database = "spd";
String username = "vxspd";
String password = "vxspd";

// 存储过程
String procedure = "vxspd.px_route.p_route_json";

// 自定义类型
String arrayType = "vxspd.type_nvarchar2";  // VARCHAR 数组类型
```

## 最佳实践

### 1. 参数处理
- 优先使用字符串方式传递数组参数
- 为 Array 对象创建提供回退机制
- 统一应用中的数组参数格式

### 2. 错误处理
- 始终使用 try-with-resources 管理资源
- 实现适当的事务回滚机制
- 记录详细的错误信息

### 3. 性能优化
- 使用连接池管理数据库连接
- 考虑批量处理大量数据
- 监控存储过程执行时间

### 4. 代码组织
- 使用工具类封装常用操作
- 分离配置和业务逻辑
- 编写单元测试验证功能

## 故障排除

### 常见问题
1. **编码问题** - 使用 UTF-8 编码编译
2. **类型不匹配** - 数组类型使用 setString() 而非 setArray()
3. **连接失败** - 检查网络、凭据和服务状态
4. **存储过程不存在** - 验证过程名称和权限

### 调试步骤
1. 运行 QuickConnectionTest 验证基础环境
2. 使用简单参数测试存储过程调用
3. 逐步增加参数复杂性
4. 检查数据库日志获取详细错误信息

## 扩展建议

### 生产环境优化
- 实现连接池（如 HikariCP）
- 添加监控和日志记录
- 实现重试机制
- 配置化参数管理

### 功能扩展
- 支持更多自定义类型
- 实现异步调用
- 添加缓存机制
- 集成 Spring Framework

## 技术支持

如果遇到问题，请：
1. 查看相关文档和示例
2. 运行连接测试工具
3. 检查数据库日志
4. 验证存储过程和类型定义

---

**注意**: 本解决方案专门针对人大金仓数据库和 `vxspd.type_nvarchar2` 数组类型设计，确保在使用前验证您的数据库环境和存储过程定义。
