package com.kingbase8;

import java.io.IOException;
import java.io.OutputStream;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.Writer;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class TestClob {

    protected String Driver = "com.kingbase8.Driver";
    protected static String Url = "*************************************";
    protected Connection connection = null;
    protected Statement statement = null;
    protected String source = "Welcome to Kingbase!";
    protected void Driver() throws ClassNotFoundException
    {
        Class.forName(Driver);
    }
    protected void connection(String url) throws SQLException,
            InterruptedException
    {
        connection = DriverManager.getConnection(url, "system", "manager");
        if(connection != null)
        {
            System.out.println("connection sucessful!");
        }
        else
        {
            System.out.println("connection fail!");
        }
    }
    protected void statement() throws SQLException
    {
        statement = connection.createStatement();
    }
    protected void table() throws SQLException
    {
        statement.executeUpdate("create table clob_table (col1 Clob, col2 " +
                "Clob, col3 Clob)");
    }
    protected void insertClob() throws SQLException
    {
        PreparedStatement pstmt = connection.prepareStatement(
                "insert into clob_table values (?,?,?)");
        StringReader reader = new StringReader(source);
        StringReader reader1 = new StringReader(source);
        StringReader reader2 = new StringReader(source);
        pstmt.setClob(1, reader);
        pstmt.setClob(2, reader1);
        pstmt.setClob(3, reader2);
        pstmt.executeUpdate();
        pstmt.close();
    }
    protected void queryClob() throws SQLException, IOException
    {
        String sql_select = "select * from clob_table";
        ResultSet resultSet = statement.executeQuery(sql_select);
        int count = 1;
        while(resultSet.next())
        {
            System.out.println(" 第" + count + " 条记录");
            String clob1 = resultSet.getString(1);
            String clob2 = resultSet.getString(2);
            String clob3 = resultSet.getString(3);
            System.out.println("clob1:" + clob1);
            System.out.println("clob2:" + clob2);
            System.out.println("clob3:" + clob3);
            count++;
        }
        resultSet.close();
        count = 1;
        resultSet = statement.executeQuery(sql_select);
        while(resultSet.next())
        {
            System.out.println(" 第" + count + " 条记录");
            for(int i = 0; i < 3; i++)
            {
                Clob clob = resultSet.getClob(i+1);
                Reader reader = clob.getCharacterStream();
                StringWriter sWriter = new StringWriter();
                int j = -1;
                while((j = reader.read()) != -1)
                {
                    sWriter.write(j);
                }
                String finalClob = new String(sWriter.getBuffer());
                System.out.println(finalClob);
            }
            count++;
        }
        resultSet.close();
    }
    protected void updateClob() throws SQLException, IOException
    {
        String sql_select = "select * from clob_table for update";
        ResultSet resultSet = statement.executeQuery(sql_select);
        PreparedStatement pstmt = connection.prepareStatement(
                "update clob_table set col1=?,col2=?,col3=?");
        if(resultSet.next())
        {
            Clob clob = resultSet.getClob(1);
            String temp = "Test has sucessful";
            System.out.println(resultSet.getString(1).length());
            OutputStream os = clob.setAsciiStream(clob.length() + 1);
            byte[] tempByte = temp.getBytes();
            os.write(tempByte);
            os.close();
            pstmt.setClob(1, clob);
            clob = resultSet.getClob(2);
            Writer writer = clob.setCharacterStream(clob.length() + 1);
            temp = "Welcome to Kingbase!";
            writer.write(temp);writer.write(temp);
            writer.close();
            pstmt.setClob(2, clob);
            clob = resultSet.getClob(3);
            temp = "Hello, Kingbase!";
            clob.setString(clob.length() + 1, temp);
            pstmt.setClob(3, clob);
            pstmt.executeUpdate();
            pstmt.close();
        }
        resultSet = statement.executeQuery(sql_select);
        if(resultSet.next())
        {
            System.out.println(" 更新后的记录");
            System.out.println(resultSet.getString(1));
            System.out.println(resultSet.getString(2));
            System.out.println(resultSet.getString(3));
        }
        resultSet.close();
    }
    public static void main(String[] args) throws ClassNotFoundException,
            SQLException, InterruptedException, IOException
    {
        TestClob test = new TestClob();
        test.Driver();
        test.connection(Url);
        test.statement();
        test.table();
        test.insertClob();
        test.queryClob();
        test.updateClob();
        test.close();
    }
    protected void close() throws SQLException
    {
        statement.executeUpdate("drop table clob_table");
        if(statement != null)
        {
            statement.close();
        }
        if(connection != null)
        {
            connection.close();
        }
    }

}
