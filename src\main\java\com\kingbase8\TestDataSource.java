package com.kingbase8;
import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Hashtable;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;
import com.kingbase8.jdbc3.Jdbc3SimpleDataSource;

public class TestDataSource {

    public static void main(String[] args) throws NamingException,
            SQLException
    {
        Jdbc3SimpleDataSource ds = new Jdbc3SimpleDataSource();
        ds.setServerName("localhost");
        ds.setDatabaseName("test");
        ds.setUser("system");
        ds.setPassword("manager");
        ds.setPortNumber(54321);
        Context ctx = null;
        File file = new File("D:\\job\\jndi");
        if (!file.exists()){
            file.mkdirs();
        }
        try
        {
            Hashtable env = new Hashtable(5);
            env.put(Context.INITIAL_CONTEXT_FACTORY,
                    "com.sun.jndi.fscontext.RefFSContextFactory");
            env.put(Context.PROVIDER_URL, "file:/job/jndi");
            ctx = new InitialContext(env);
        }
        catch (NamingException ne)
        {
            ne.printStackTrace();
        }
        ctx.rebind("DataSource", ds);
        Context ctx2 = null;
        try
        {
            Hashtable env = new Hashtable(5);
            env.put(Context.INITIAL_CONTEXT_FACTORY,
                    "com.sun.jndi.fscontext.RefFSContextFactory");
            env.put(Context.PROVIDER_URL, "file:/job/jndi");
            ctx2 = new InitialContext(env);
        }
        catch (NamingException ne)
        {
            ne.printStackTrace();
        }
        DataSource ds2 = (DataSource) ctx2.lookup("DataSource");
        Connection conn = ds2.getConnection();
        Statement stmt = conn.createStatement();
        stmt.executeUpdate("create table dataSource (id int)");
        stmt.executeUpdate("insert into dataSource values (1)");
        ResultSet rs = stmt.executeQuery("select * from dataSource");
        while(rs.next())
        {
            System.out.println("id:"+ rs.getInt(1));
        }
        stmt.executeUpdate("drop table dataSource");
        rs.close();
        stmt.close();
        conn.close();
    }
}
