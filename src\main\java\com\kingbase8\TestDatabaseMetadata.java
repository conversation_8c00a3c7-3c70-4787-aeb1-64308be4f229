package com.kingbase8;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.SQLException;

public class TestDatabaseMetadata {

    protected String Driver = "com.kingbase8.Driver";
    protected static String Url = "*************************************";
    protected Connection connection = null;
    protected void Driver() throws ClassNotFoundException
    {
        Class.forName(Driver);
    }
    protected void connection(String url) throws SQLException,
            InterruptedException
    {
        connection = DriverManager.getConnection(url, "system", "manager");
        if(connection != null)
        {
            System.out.println("connection sucessful!");
        }
        else
        {
            System.out.println("connection fail!");
        }
    }
    public static void main(String[] args) throws ClassNotFoundException,
            SQLException, InterruptedException
    {
        TestDatabaseMetadata test = new TestDatabaseMetadata();
        test.Driver();
        test.connection(Url);
        test.metaDate();
        test.close();
    }
    protected void metaDate() throws SQLException
    {
        DatabaseMetaData metaDate = connection.getMetaData();
        Connection connection = metaDate.getConnection();
        int DatabaseMajorVersion = metaDate.getDatabaseMajorVersion();
        int JDBCMajorVersion = metaDate.getJDBCMajorVersion();
        System.out.println(JDBCMajorVersion);
    }
    protected void close() throws SQLException {
        if (connection != null) {
            connection.close();
        }
    }
}
